<?php

namespace Singleton;

final class Logger {
    private static ?Logger $instance = null;

    // Prevent external instantiation
    private function __construct() {}

    // Prevent external cloning
    private function __clone(): void {}

    // Prevent unserialization
    public function __wakeup(): void {
        throw new \Exception("Cannot unserialize singleton");
    }

    // Global access point
    public static function getInstance(): Logger {
        return self::$instance ??= new self();
    }

    public function log(string $message): void {
        echo '[' . date('Y-m-d H:i:s') . '] ' . $message . "\n";
    }
}
