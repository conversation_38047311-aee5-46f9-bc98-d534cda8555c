<?php

namespace PasswordValidation;

class EntropyValidator implements PasswordValidatorStrategy {
    public function isValid(string $password): bool {
        $entropy = $this->calculateEntropy($password);
        return $entropy >= 3;
    }

    private function calculateEntropy(string $password): float {
        $charCount = count_chars($password, 1);
        $totalChars = strlen($password);
        $entropy = 0;

        foreach ($charCount as $count) {
            $probability = $count / $totalChars;
            $entropy -= $probability * log($probability, 2);
        }

        return $entropy;
    }
}