#!/usr/bin/env python3
"""
Demo script for the Strategy Design Pattern implementation in Python.
Equivalent to the PHP index.php file.
"""

from password_checker import <PERSON><PERSON><PERSON><PERSON><PERSON>
from length_validator import <PERSON><PERSON><PERSON><PERSON><PERSON>
from pattern_validator import <PERSON><PERSON>V<PERSON>dator
from entropy_validator import Entropy<PERSON><PERSON><PERSON><PERSON>


def main():
    """Demonstrate the password validation strategies."""
    
    # Test passwords
    test_passwords = [
        "password",           # Short, simple
        "MySecureP@ssw0rd!",  # Long, complex
        "short",              # Very short
        "verylongpasswordbutnosymbols123",  # Long but no special chars
    ]
    
    # Test with different strategies
    strategies = [
        ("Length Validator", LengthValidator()),
        ("Pattern Validator", PatternValidator()),
        ("Entropy Validator", EntropyValidator()),
    ]
    
    for password in test_passwords:
        print(f"\nTesting password: '{password}'")
        print("-" * 40)
        
        for strategy_name, strategy in strategies:
            checker = PasswordChecker(strategy)
            result = "valid" if checker.check(password) else "invalid"
            print(f"{strategy_name}: {result}")


if __name__ == "__main__":
    main()
