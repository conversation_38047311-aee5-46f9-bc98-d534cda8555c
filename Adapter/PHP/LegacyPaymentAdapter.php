<?php

namespace Adapter;

class LegacyPaymentAdapter implements PaymentProcessor {
    private LegacyPayment $legacyPayment;

    public function __construct(LegacyPayment $legacyPayment) {
        $this->legacyPayment = $legacyPayment;
    }

    public function processPayment(float $amount): void {
        $moneyInCents = (int) round($amount * 100);
        $this->legacyPayment->makePayment($moneyInCents);
    }
}
